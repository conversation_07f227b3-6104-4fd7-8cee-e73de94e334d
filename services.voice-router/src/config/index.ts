import dotenv from 'dotenv';
import { mediasoupConfig } from './mediasoup.config.ts';

dotenv.config();

const config = {
  http: {
    host: process.env.LISTEN_HOST || '0.0.0.0',
    port: parseInt(process.env.LISTEN_PORT || '3000', 10),
  },
  mediasoup: {
    // Number of MediaSoup workers to launch.
    // Defaults to the number of CPU cores.
    numWorkers: Number(process.env.MEDIASOUP_WORKERS) || require('os').cpus().length,

    // Merge static mediasoup config
   ...mediasoupConfig,
  },
};

export default config;