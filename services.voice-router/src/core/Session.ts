import { types as mediasoupTypes } from 'mediasoup';

/**
 * Represents a single media session, encapsulating a MediaSoup Router
 * and all its associated resources.
 */
export class Session {
  public readonly id: string;
  private router: mediasoupTypes.Router | null = null;
  private worker: mediasoupTypes.Worker;

  constructor(worker: mediasoupTypes.Worker) {
    this.worker = worker;
    this.id = ''; // The router ID will be assigned on start
  }

  /**
   * Initializes the session by creating a MediaSoup Router.
   */
  public async start(): Promise<void> {
    this.router = await this.worker.createRouter({
      // See mediasoup documentation for router options
      // https://mediasoup.org/documentation/v3/mediasoup/api/#RouterOptions
      mediaCodecs: [
        {
          kind        : "audio",
          mimeType    : "audio/opus",
          clockRate   : 48000,
          channels    : 2
        },
      ]
    });

    // Assign the router's ID as the session's public ID
    (this as { id: string }).id = this.router.id;
  }

  /**
   * Starts an RTP fork to a downstream service.
   * @param forkOptions - Details of the RTP endpoint.
   * @returns The unique ID of the new fork.
   */
  public async startRtpFork(forkOptions: { ip: string; port: number }): Promise<string> {
    if (!this.router) throw new Error('Session not started');

    console.log(` Starting RTP fork to ${forkOptions.ip}:${forkOptions.port}`);
    // TODO: Implementation for creating a PlainTransport and Consumers
    // 1. Create a PlainTransport
    // 2. Create a Consumer for each existing Producer in the router
    // 3. Return a unique fork_id

    return `fork-${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Closes the session and all its resources.
   */
  public close(): void {
    if (this.router) {
      this.router.close();
      this.router = null;
      console.log(` Closed.`);
    }
  }
}