import * as mediasoup from 'mediasoup';
import { types as mediasoupTypes } from 'mediasoup';
import config from '../config';

/**
 * Manages the pool of MediaSoup Worker processes.
 * Implemented as a singleton to ensure a single source of workers.
 */
export class MediaServer {
  private static instance: MediaServer;
  private workers: mediasoupTypes.Worker[] = [];
  private nextWorkerIndex = 0;

  private constructor() {}

  public static getInstance(): MediaServer {
    if (!MediaServer.instance) {
      MediaServer.instance = new MediaServer();
    }
    return MediaServer.instance;
  }

  /**
   * Initializes the MediaSoup workers based on the configuration.
   */
  public async start(options: { numWorkers: number }): Promise<void> {
    console.log(`Starting ${options.numWorkers} MediaSoup worker(s)...`);

    for (let i = 0; i < options.numWorkers; i++) {
      const worker = await mediasoup.createWorker({
        logLevel: config.mediasoup.worker.logLevel,
        logTags: config.mediasoup.worker.logTags,
        rtcMinPort: config.mediasoup.worker.rtcMinPort,
        rtcMaxPort: config.mediasoup.worker.rtcMaxPort,
      });

      worker.on('died', (error: any) => {
        console.error(`MediaSoup worker ${worker.pid} has died:`, error);
        // In a production environment, you would want to handle this gracefully,
        // perhaps by creating a new worker and migrating sessions if possible.
        process.exit(1);
      });

      this.workers.push(worker);
      console.log(`--> MediaSoup worker ${worker.pid} started.`);
    }
  }

  /**
   * Gets the next available worker in a round-robin fashion.
   * @returns A MediaSoup Worker instance.
   */
  public getWorker(): mediasoupTypes.Worker {
    if (this.workers.length === 0) {
      throw new Error('No MediaSoup workers available. Did you call start()?');
    }

    const worker = this.workers[this.nextWorkerIndex];
    this.nextWorkerIndex = (this.nextWorkerIndex + 1) % this.workers.length;
    return worker;
  }
}