import { Router } from 'express';
import { SessionManager } from '../../services/SessionManager';
import * as controller from './controller';

export function createApiRouter(sessionManager: SessionManager): Router {
  const router = Router();

  // Middleware to inject the SessionManager into each request
  router.use((req, res, next) => {
    req.sessionManager = sessionManager;
    next();
  });

  /**
   * Creates a new media session.
   * POST /internal/calls
   */
  router.post('/internal/calls', controller.createCall);

  /**
   * Terminates an entire media session.
   * DELETE /internal/calls/{sessionId}
   */
  router.delete('/internal/calls/:sessionId', controller.deleteCall);

  /**
   * Starts an audio fork for a given session.
   * POST /internal/calls/{sessionId}/forks
   */
  router.post('/internal/calls/:sessionId/forks', controller.startFork);

  /**
   * Stops a specific audio fork.
   * DELETE /internal/calls/{sessionId}/forks/{forkId}
   */
  router.delete('/internal/calls/:sessionId/forks/:forkId', controller.stopFork);

  return router;
}